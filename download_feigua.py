# 数据下载
import os
import xbot
import datetime
import asyncio
import time
import json

from xbot import print, sleep, web, win32
from .import package
from .package import variables as glv
from .logger import logger
from .utils import safe_find, safe_find_all, is_exist, get_feigua_competitor_file_name
from .fg_login import login
from .notify import notify

target_url = "https://dy.feigua.cn/app/#/brand-detail/index"


def downloadFeigua(brand, yesterday, target_date, folder):
    # 文件名
    file_name = get_feigua_competitor_file_name(brand["brand_name"], target_date)
    
    # 判断文件是否存在，都存在则直接返回
    if os.path.exists(os.path.join(folder, file_name)):
        return

    page = login("网易严选", target_url)

    time.sleep(10)


# 具体下载数据流程
def download_data(page, yesterday, target_date, folder, file_name):
    

    pass




def main(args):
    # download('网易严选洗护清洁旗舰店', '2025-07-08')
    # page = web.get_active('chrome')
    # select_date(page, '2024-07-09')
    # select_metric(page)
    pass
